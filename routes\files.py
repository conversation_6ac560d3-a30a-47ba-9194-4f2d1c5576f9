from flask import Blueprint, request, jsonify, send_file
from werkzeug.utils import secure_filename
from PIL import Image
import os
import uuid
import mimetypes
from models.user import User

files_bp = Blueprint('files', __name__)
user_model = User()

UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'webp', 'mp4', 'avi', 'mov', 'webm', 'doc', 'docx', 'csv'}
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_user_from_token(request):
    """Extract user from authorization token"""
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return None
    
    session_token = auth_header.split(' ')[1]
    return user_model.get_user_by_session(session_token)

@files_bp.route('/upload', methods=['POST'])
def upload_file():
    """Upload a file"""
    try:
        # Check authentication
        user_data = get_user_from_token(request)
        if not user_data:
            return jsonify({
                'success': False,
                'error': 'Authentication required'
            }), 401
        
        # Check usage limit
        usage = user_model.get_user_usage(user_data['id'])
        limits = user_model.get_plan_limits(user_data['plan'])
        
        if usage.get('upload', 0) >= limits.get('upload', 0):
            return jsonify({
                'success': False,
                'error': 'Upload limit reached. Please upgrade your plan.'
            }), 429
        
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No file provided'
            }), 400
        
        file = request.files['file']
        
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'No file selected'
            }), 400
        
        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'error': 'File type not allowed'
            }), 400
        
        # Check file size
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)
        
        if file_size > MAX_FILE_SIZE:
            return jsonify({
                'success': False,
                'error': 'File too large. Maximum size is 100MB'
            }), 413
        
        # Generate unique filename
        file_id = str(uuid.uuid4())
        filename = secure_filename(file.filename)
        file_extension = filename.rsplit('.', 1)[1].lower()
        stored_filename = f"{file_id}.{file_extension}"
        
        # Create upload directory if it doesn't exist
        os.makedirs(UPLOAD_FOLDER, exist_ok=True)
        
        # Save file
        file_path = os.path.join(UPLOAD_FOLDER, stored_filename)
        file.save(file_path)
        
        # Get file info
        file_type = mimetypes.guess_type(filename)[0] or 'application/octet-stream'
        
        # Increment usage
        user_model.increment_usage(user_data['id'], 'upload')
        
        return jsonify({
            'success': True,
            'file_id': file_id,
            'filename': filename,
            'size': file_size,
            'type': file_type,
            'message': 'File uploaded successfully'
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Upload failed: ' + str(e)
        }), 500

@files_bp.route('/compress', methods=['POST'])
def compress_file():
    """Compress an image file"""
    try:
        # Check authentication
        user_data = get_user_from_token(request)
        if not user_data:
            return jsonify({
                'success': False,
                'error': 'Authentication required'
            }), 401
        
        # Check usage limit
        usage = user_model.get_user_usage(user_data['id'])
        limits = user_model.get_plan_limits(user_data['plan'])
        
        if usage.get('compress', 0) >= limits.get('compress', 0):
            return jsonify({
                'success': False,
                'error': 'Compression limit reached. Please upgrade your plan.'
            }), 429
        
        data = request.get_json()
        file_id = data.get('file_id')
        quality = int(data.get('quality', 80))
        
        if not file_id:
            return jsonify({
                'success': False,
                'error': 'File ID is required'
            }), 400
        
        # Find the file
        file_path = None
        for ext in ['jpg', 'jpeg', 'png', 'webp', 'gif']:
            potential_path = os.path.join(UPLOAD_FOLDER, f"{file_id}.{ext}")
            if os.path.exists(potential_path):
                file_path = potential_path
                break
        
        if not file_path:
            return jsonify({
                'success': False,
                'error': 'File not found'
            }), 404
        
        # Get original file size
        original_size = os.path.getsize(file_path)
        
        # Compress image
        with Image.open(file_path) as img:
            # Convert RGBA to RGB if necessary
            if img.mode in ('RGBA', 'LA'):
                background = Image.new('RGB', img.size, (255, 255, 255))
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background
            
            # Save compressed version
            compressed_path = os.path.join(UPLOAD_FOLDER, f"{file_id}_compressed.jpg")
            img.save(compressed_path, 'JPEG', quality=quality, optimize=True)
        
        # Get compressed file size
        compressed_size = os.path.getsize(compressed_path)
        compression_ratio = round((1 - compressed_size / original_size) * 100, 1)
        
        # Increment usage
        user_model.increment_usage(user_data['id'], 'compress')
        
        return jsonify({
            'success': True,
            'original_size': original_size,
            'compressed_size': compressed_size,
            'compression_ratio': compression_ratio,
            'compressed_file_id': f"{file_id}_compressed",
            'message': f'Image compressed successfully. Size reduced by {compression_ratio}%'
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Compression failed: ' + str(e)
        }), 500

@files_bp.route('/convert', methods=['POST'])
def convert_file():
    """Convert file format"""
    try:
        # Check authentication
        user_data = get_user_from_token(request)
        if not user_data:
            return jsonify({
                'success': False,
                'error': 'Authentication required'
            }), 401
        
        # Check usage limit
        usage = user_model.get_user_usage(user_data['id'])
        limits = user_model.get_plan_limits(user_data['plan'])
        
        if usage.get('convert', 0) >= limits.get('convert', 0):
            return jsonify({
                'success': False,
                'error': 'Conversion limit reached. Please upgrade your plan.'
            }), 429
        
        data = request.get_json()
        file_id = data.get('file_id')
        target_format = data.get('target_format', 'jpg').lower()
        
        if not file_id:
            return jsonify({
                'success': False,
                'error': 'File ID is required'
            }), 400
        
        if target_format not in ['jpg', 'jpeg', 'png', 'webp']:
            return jsonify({
                'success': False,
                'error': 'Unsupported target format'
            }), 400
        
        # Find the file
        file_path = None
        original_ext = None
        for ext in ['jpg', 'jpeg', 'png', 'webp', 'gif']:
            potential_path = os.path.join(UPLOAD_FOLDER, f"{file_id}.{ext}")
            if os.path.exists(potential_path):
                file_path = potential_path
                original_ext = ext
                break
        
        if not file_path:
            return jsonify({
                'success': False,
                'error': 'File not found'
            }), 404
        
        # Convert image
        with Image.open(file_path) as img:
            # Handle different formats
            if target_format in ['jpg', 'jpeg']:
                if img.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                save_format = 'JPEG'
            elif target_format == 'png':
                save_format = 'PNG'
            elif target_format == 'webp':
                save_format = 'WebP'
            
            # Save converted version
            converted_path = os.path.join(UPLOAD_FOLDER, f"{file_id}_converted.{target_format}")
            img.save(converted_path, save_format, quality=95, optimize=True)
        
        # Increment usage
        user_model.increment_usage(user_data['id'], 'convert')
        
        return jsonify({
            'success': True,
            'original_format': original_ext,
            'target_format': target_format,
            'converted_file_id': f"{file_id}_converted",
            'message': f'File successfully converted from {original_ext} to {target_format}'
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Conversion failed: ' + str(e)
        }), 500

@files_bp.route('/analyze', methods=['POST'])
def analyze_file():
    """Analyze file with AI using DeepSeek"""
    try:
        # Check authentication
        user_data = get_user_from_token(request)
        if not user_data:
            return jsonify({
                'success': False,
                'error': 'Authentication required'
            }), 401
        
        # Check usage limit
        usage = user_model.get_user_usage(user_data['id'])
        limits = user_model.get_plan_limits(user_data['plan'])
        
        if usage.get('analyze', 0) >= limits.get('analyze', 0):
            return jsonify({
                'success': False,
                'error': 'Analysis limit reached. Please upgrade your plan.'
            }), 429
        
        data = request.get_json()
        file_id = data.get('file_id')
        
        if not file_id:
            return jsonify({
                'success': False,
                'error': 'File ID is required'
            }), 400
        
        # Find the file
        file_path = None
        file_ext = None
        for ext in ALLOWED_EXTENSIONS:
            potential_path = os.path.join(UPLOAD_FOLDER, f"{file_id}.{ext}")
            if os.path.exists(potential_path):
                file_path = potential_path
                file_ext = ext
                break
        
        if not file_path:
            return jsonify({
                'success': False,
                'error': 'File not found'
            }), 404
        
        # Import AI service
        from services.ai_service import ai_service
        
        # Get AI analysis
        analysis_result = ai_service.get_analysis_for_file(file_path, file_ext)
        
        # Increment usage
        user_model.increment_usage(user_data['id'], 'analyze')
        
        return jsonify({
            'success': True,
            'analysis': analysis_result,
            'message': 'File analysis completed using DeepSeek AI'
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Analysis failed: ' + str(e)
        }), 500

@files_bp.route('/download/<file_id>', methods=['GET'])
def download_file(file_id):
    """Download a file"""
    try:
        # Check authentication
        user_data = get_user_from_token(request)
        if not user_data:
            return jsonify({
                'success': False,
                'error': 'Authentication required'
            }), 401
        
        # Find the file
        file_path = None
        original_filename = None
        
        for ext in ALLOWED_EXTENSIONS:
            potential_path = os.path.join(UPLOAD_FOLDER, f"{file_id}.{ext}")
            if os.path.exists(potential_path):
                file_path = potential_path
                original_filename = f"download.{ext}"
                break
        
        if not file_path:
            return jsonify({
                'success': False,
                'error': 'File not found'
            }), 404
        
        return send_file(file_path, as_attachment=True, download_name=original_filename)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Download failed: ' + str(e)
        }), 500

@files_bp.route('/list', methods=['GET'])
def list_files():
    """List user's uploaded files"""
    try:
        # Check authentication
        user_data = get_user_from_token(request)
        if not user_data:
            return jsonify({
                'success': False,
                'error': 'Authentication required'
            }), 401
        
        # In a real implementation, you would store file metadata in database
        # For now, return empty list as this is a demo
        return jsonify({
            'success': True,
            'files': [],
            'message': 'File listing feature will be enhanced with database integration'
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Failed to list files: ' + str(e)
        }), 500

