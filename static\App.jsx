import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from './components/ui/button.jsx'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './components/ui/card.jsx'
import { Input } from './components/ui/input.jsx'
import { Label } from './components/ui/label.jsx'
// import { Upload, Zap, RefreshCw, Brain, Share2, FileImage, Video, FileText, Download, Loader2, User, LogOut, Crown, Check, Github } from 'lucide-react'
import { api } from './api.js'
// import pinkonLogo from './assets/pinkon-logo-new.png'
import './App.css'

function App() {
  const [activeTab, setActiveTab] = useState('home')
  const [uploadedFile, setUploadedFile] = useState(null)
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState(null)
  const [error, setError] = useState(null)
  const [user, setUser] = useState(null)
  const [showAuth, setShowAuth] = useState(false)
  const [authMode, setAuthMode] = useState('login') // 'login' or 'register'

  // Mock user data for demonstration
  useEffect(() => {
    const savedUser = localStorage.getItem('pinkon_user')
    if (savedUser) {
      setUser(JSON.parse(savedUser))
    }
  }, [])

  const handleLogin = async (email, password) => {
    setLoading(true)
    try {
      // Mock login - in real app, call API
      const mockUser = {
        id: 1,
        email: email,
        name: email.split('@')[0],
        plan: 'free',
        usage: {
          upload: 2,
          compress: 1,
          convert: 0,
          analyze: 1
        },
        limits: {
          upload: 5,
          compress: 5,
          convert: 5,
          analyze: 5
        }
      }
      setUser(mockUser)
      localStorage.setItem('pinkon_user', JSON.stringify(mockUser))
      setShowAuth(false)
    } catch (err) {
      setError('Login failed: ' + err.message)
    }
    setLoading(false)
  }

  const handleRegister = async (email, password, name) => {
    setLoading(true)
    try {
      // Mock registration - in real app, call API
      const mockUser = {
        id: Date.now(),
        email: email,
        name: name,
        plan: 'free',
        usage: {
          upload: 0,
          compress: 0,
          convert: 0,
          analyze: 0
        },
        limits: {
          upload: 5,
          compress: 5,
          convert: 5,
          analyze: 5
        }
      }
      setUser(mockUser)
      localStorage.setItem('pinkon_user', JSON.stringify(mockUser))
      setShowAuth(false)
    } catch (err) {
      setError('Registration failed: ' + err.message)
    }
    setLoading(false)
  }

  const handleLogout = () => {
    setUser(null)
    localStorage.removeItem('pinkon_user')
    setActiveTab('home')
  }

  const handleOAuthLogin = (provider) => {
    // Mock OAuth login
    const mockUser = {
      id: Date.now(),
      email: `user@${provider}.com`,
      name: `${provider} User`,
      plan: 'free',
      usage: {
        upload: 0,
        compress: 0,
        convert: 0,
        analyze: 0
      },
      limits: {
        upload: 5,
        compress: 5,
        convert: 5,
        analyze: 5
      }
    }
    setUser(mockUser)
    localStorage.setItem('pinkon_user', JSON.stringify(mockUser))
    setShowAuth(false)
  }

  const handleFileUpload = async (file) => {
    if (!user) {
      setError('Please login to upload files')
      setShowAuth(true)
      return
    }

    if (user.usage.upload >= user.limits.upload) {
      setError('Upload limit reached. Please upgrade your plan.')
      setActiveTab('pricing')
      return
    }

    setLoading(true)
    setError(null)
    try {
      const response = await api.uploadFile(file)
      if (response.success) {
        setUploadedFile(response)
        setResult(`File uploaded successfully! File ID: ${response.file_id}`)
        // Update user usage
        const updatedUser = {
          ...user,
          usage: {
            ...user.usage,
            upload: user.usage.upload + 1
          }
        }
        setUser(updatedUser)
        localStorage.setItem('pinkon_user', JSON.stringify(updatedUser))
      } else {
        setError(response.error || 'Upload failed')
      }
    } catch (err) {
      setError('Upload failed: ' + err.message)
    }
    setLoading(false)
  }

  const handleCompress = async (quality) => {
    if (!user) {
      setError('Please login to use compression')
      setShowAuth(true)
      return
    }

    if (user.usage.compress >= user.limits.compress) {
      setError('Compression limit reached. Please upgrade your plan.')
      setActiveTab('pricing')
      return
    }

    if (!uploadedFile) {
      setError('Please upload a file first')
      return
    }
    
    setLoading(true)
    setError(null)
    try {
      const response = await api.compressFile(uploadedFile.file_id, quality)
      if (response.success) {
        setResult(`Compression successful! Reduced size by ${response.compression_ratio}%`)
        // Update user usage
        const updatedUser = {
          ...user,
          usage: {
            ...user.usage,
            compress: user.usage.compress + 1
          }
        }
        setUser(updatedUser)
        localStorage.setItem('pinkon_user', JSON.stringify(updatedUser))
      } else {
        setError(response.error || 'Compression failed')
      }
    } catch (err) {
      setError('Compression failed: ' + err.message)
    }
    setLoading(false)
  }

  const handleConvert = async (targetFormat) => {
    if (!user) {
      setError('Please login to use conversion')
      setShowAuth(true)
      return
    }

    if (user.usage.convert >= user.limits.convert) {
      setError('Conversion limit reached. Please upgrade your plan.')
      setActiveTab('pricing')
      return
    }

    if (!uploadedFile) {
      setError('Please upload a file first')
      return
    }
    
    setLoading(true)
    setError(null)
    try {
      const response = await api.convertFile(uploadedFile.file_id, targetFormat)
      if (response.success) {
        setResult(`Conversion to ${targetFormat} successful!`)
        // Update user usage
        const updatedUser = {
          ...user,
          usage: {
            ...user.usage,
            convert: user.usage.convert + 1
          }
        }
        setUser(updatedUser)
        localStorage.setItem('pinkon_user', JSON.stringify(updatedUser))
      } else {
        setError(response.error || 'Conversion failed')
      }
    } catch (err) {
      setError('Conversion failed: ' + err.message)
    }
    setLoading(false)
  }

  const handleAnalyze = async () => {
    if (!user) {
      setError('Please login to use AI analysis')
      setShowAuth(true)
      return
    }

    if (user.usage.analyze >= user.limits.analyze) {
      setError('Analysis limit reached. Please upgrade your plan.')
      setActiveTab('pricing')
      return
    }

    if (!uploadedFile) {
      setError('Please upload a file first')
      return
    }
    
    setLoading(true)
    setError(null)
    try {
      const response = await api.analyzeFile(uploadedFile.file_id)
      if (response.success) {
        setResult(response.analysis)
        // Update user usage
        const updatedUser = {
          ...user,
          usage: {
            ...user.usage,
            analyze: user.usage.analyze + 1
          }
        }
        setUser(updatedUser)
        localStorage.setItem('pinkon_user', JSON.stringify(updatedUser))
      } else {
        setError(response.error || 'Analysis failed')
      }
    } catch (err) {
      setError('Analysis failed: ' + err.message)
    }
    setLoading(false)
  }

  const features = [
    {
      icon: <Share2 className="h-8 w-8 text-primary" />,
      title: "File Sharing",
      description: "Share files securely with expiration links and password protection"
    },
    {
      icon: <Zap className="h-8 w-8 text-primary" />,
      title: "Smart Compression",
      description: "Reduce file sizes without quality loss using advanced algorithms"
    },
    {
      icon: <RefreshCw className="h-8 w-8 text-primary" />,
      title: "Format Conversion",
      description: "Convert between multiple file formats with ease"
    },
    {
      icon: <Brain className="h-8 w-8 text-primary" />,
      title: "AI Analysis",
      description: "Get insights about your files with AI-powered analysis"
    }
  ]

  const plans = [
    {
      name: "Free",
      price: "$0",
      period: "/month",
      description: "Perfect for trying out Pinkon",
      features: [
        "5 file uploads per month",
        "5 compressions per month",
        "5 conversions per month",
        "5 AI analyses per month",
        "Basic support"
      ],
      limits: { upload: 5, compress: 5, convert: 5, analyze: 5 },
      popular: false
    },
    {
      name: "Pro",
      price: "$5",
      period: "/month",
      description: "Great for regular users",
      features: [
        "100 file uploads per month",
        "100 compressions per month",
        "100 conversions per month",
        "100 AI analyses per month",
        "Priority support",
        "Advanced features"
      ],
      limits: { upload: 100, compress: 100, convert: 100, analyze: 100 },
      popular: true
    },
    {
      name: "Premium",
      price: "$10",
      period: "/month",
      description: "Best for power users",
      features: [
        "200 file uploads per month",
        "200 compressions per month",
        "200 conversions per month",
        "200 AI analyses per month",
        "24/7 premium support",
        "All advanced features",
        "API access"
      ],
      limits: { upload: 200, compress: 200, convert: 200, analyze: 200 },
      popular: false
    }
  ]

  const AuthModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-md mx-4">
        <CardHeader>
          <CardTitle>{authMode === 'login' ? 'Login to Pinkon' : 'Create Account'}</CardTitle>
          <CardDescription>
            {authMode === 'login' ? 'Welcome back!' : 'Join thousands of users'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={(e) => {
            e.preventDefault()
            const formData = new FormData(e.target)
            if (authMode === 'login') {
              handleLogin(formData.get('email'), formData.get('password'))
            } else {
              handleRegister(formData.get('email'), formData.get('password'), formData.get('name'))
            }
          }}>
            <div className="space-y-4">
              {authMode === 'register' && (
                <div>
                  <Label htmlFor="name">Full Name</Label>
                  <Input id="name" name="name" type="text" required />
                </div>
              )}
              <div>
                <Label htmlFor="email">Email</Label>
                <Input id="email" name="email" type="email" required />
              </div>
              <div>
                <Label htmlFor="password">Password</Label>
                <Input id="password" name="password" type="password" required />
              </div>
              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                {authMode === 'login' ? 'Login' : 'Create Account'}
              </Button>
            </div>
          </form>
          
          <div className="mt-4">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">Or continue with</span>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <Button variant="outline" onClick={() => handleOAuthLogin('google')}>
                <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                  <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                Google
              </Button>
              <Button variant="outline" onClick={() => handleOAuthLogin('github')}>
                <Github className="mr-2 h-4 w-4" />
                GitHub
              </Button>
            </div>
          </div>
          
          <div className="mt-4 text-center">
            <Button 
              variant="link" 
              onClick={() => setAuthMode(authMode === 'login' ? 'register' : 'login')}
            >
              {authMode === 'login' ? "Don't have an account? Sign up" : "Already have an account? Login"}
            </Button>
          </div>
          
          <div className="mt-4 text-center">
            <Button variant="ghost" onClick={() => setShowAuth(false)}>
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const HomePage = () => (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-purple-50">
      {/* Header */}
      <header className="bg-white/90 backdrop-blur-md border-b border-purple-100 sticky top-0 z-40 shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <img src={pinkonLogo} alt="Pinkon" className="h-10 w-10" />
              <span className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-purple-400 bg-clip-text text-transparent">
                Pinkon
              </span>
            </div>
            <nav className="hidden md:flex items-center space-x-8">
              <button 
                onClick={() => setActiveTab('home')}
                className={`text-sm font-medium transition-colors ${activeTab === 'home' ? 'text-primary' : 'text-gray-600 hover:text-primary'}`}
              >
                Home
              </button>
              <button 
                onClick={() => setActiveTab('upload')}
                className={`text-sm font-medium transition-colors ${activeTab === 'upload' ? 'text-primary' : 'text-gray-600 hover:text-primary'}`}
              >
                Upload
              </button>
              <button 
                onClick={() => setActiveTab('compress')}
                className={`text-sm font-medium transition-colors ${activeTab === 'compress' ? 'text-primary' : 'text-gray-600 hover:text-primary'}`}
              >
                Compress
              </button>
              <button 
                onClick={() => setActiveTab('convert')}
                className={`text-sm font-medium transition-colors ${activeTab === 'convert' ? 'text-primary' : 'text-gray-600 hover:text-primary'}`}
              >
                Convert
              </button>
              <button 
                onClick={() => setActiveTab('analyze')}
                className={`text-sm font-medium transition-colors ${activeTab === 'analyze' ? 'text-primary' : 'text-gray-600 hover:text-primary'}`}
              >
                Analyze
              </button>
              <button 
                onClick={() => setActiveTab('pricing')}
                className={`text-sm font-medium transition-colors ${activeTab === 'pricing' ? 'text-primary' : 'text-gray-600 hover:text-primary'}`}
              >
                Pricing
              </button>
            </nav>
            <div className="flex items-center space-x-4">
              {user ? (
                <div className="flex items-center space-x-4">
                  <div className="text-sm">
                    <div className="font-medium">{user.name}</div>
                    <div className="text-gray-500 capitalize">{user.plan} Plan</div>
                  </div>
                  <Button variant="ghost" size="sm" onClick={handleLogout}>
                    <LogOut className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <Button 
                  className="bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600"
                  onClick={() => setShowAuth(true)}
                >
                  <User className="mr-2 h-4 w-4" />
                  Login
                </Button>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-24 px-4">
        <div className="container mx-auto text-center">
          <div className="mb-8">
            <img src={pinkonLogo} alt="Pinkon" className="h-20 w-20 mx-auto mb-6" />
          </div>
          <h1 className="text-6xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight">
            Transform Your Files with{' '}
            <span className="bg-gradient-to-r from-purple-600 to-purple-400 bg-clip-text text-transparent">
              AI-Powered Tools
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-10 max-w-3xl mx-auto leading-relaxed">
            Upload, compress, convert, and analyze your files with ease. 
            Experience the future of file management with our intelligent platform.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Button 
              size="lg" 
              className="bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600 text-lg px-10 py-4 h-auto shadow-lg hover:shadow-xl transition-all"
              onClick={() => user ? setActiveTab('upload') : setShowAuth(true)}
            >
              <Upload className="mr-2 h-5 w-5" />
              Start Uploading
            </Button>
            <Button 
              size="lg" 
              variant="outline" 
              className="border-2 border-purple-300 text-purple-600 hover:bg-purple-50 text-lg px-10 py-4 h-auto"
              onClick={() => setActiveTab('pricing')}
            >
              View Pricing
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 px-4 bg-white">
        <div className="container mx-auto">
          <div className="text-center mb-20">
            <h2 className="text-5xl font-bold text-gray-900 mb-6">
              Powerful Features for Every Need
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              From simple file sharing to advanced AI analysis, we've got you covered
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="border-purple-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group">
                <CardHeader className="text-center pb-4">
                  <div className="mx-auto mb-6 p-4 bg-gradient-to-br from-purple-100 to-purple-50 rounded-2xl w-fit group-hover:scale-110 transition-transform">
                    {feature.icon}
                  </div>
                  <CardTitle className="text-xl mb-2">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 px-4 bg-gradient-to-r from-purple-600 via-purple-500 to-purple-600">
        <div className="container mx-auto text-center">
          <h2 className="text-5xl font-bold text-white mb-6">
            Ready to Transform Your Files?
          </h2>
          <p className="text-xl text-purple-100 mb-10 max-w-2xl mx-auto">
            Join thousands of users who trust Pinkon for their file management needs
          </p>
          <Button 
            size="lg" 
            className="bg-white text-purple-600 hover:bg-gray-100 text-lg px-10 py-4 h-auto shadow-lg hover:shadow-xl transition-all"
            onClick={() => user ? setActiveTab('upload') : setShowAuth(true)}
          >
            Get Started Now
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16 px-4">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <img src={pinkonLogo} alt="Pinkon" className="h-8 w-8 brightness-0 invert" />
                <span className="text-xl font-bold">Pinkon</span>
              </div>
              <p className="text-gray-400 leading-relaxed">
                Transform your files with AI-powered tools
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-6 text-lg">Features</h3>
              <ul className="space-y-3 text-gray-400">
                <li className="hover:text-white transition-colors cursor-pointer">File Sharing</li>
                <li className="hover:text-white transition-colors cursor-pointer">Compression</li>
                <li className="hover:text-white transition-colors cursor-pointer">Format Conversion</li>
                <li className="hover:text-white transition-colors cursor-pointer">AI Analysis</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-6 text-lg">Support</h3>
              <ul className="space-y-3 text-gray-400">
                <li className="hover:text-white transition-colors cursor-pointer">Help Center</li>
                <li className="hover:text-white transition-colors cursor-pointer">Contact Us</li>
                <li className="hover:text-white transition-colors cursor-pointer">Privacy Policy</li>
                <li className="hover:text-white transition-colors cursor-pointer">Terms of Service</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-6 text-lg">Company</h3>
              <ul className="space-y-3 text-gray-400">
                <li className="hover:text-white transition-colors cursor-pointer">About Us</li>
                <li className="hover:text-white transition-colors cursor-pointer">Blog</li>
                <li className="hover:text-white transition-colors cursor-pointer">Careers</li>
                <li className="hover:text-white transition-colors cursor-pointer">Press</li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Pinkon. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )

  const PricingPage = () => (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-white">
      <header className="bg-white/90 backdrop-blur-md border-b border-purple-100 sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <img src={pinkonLogo} alt="Pinkon" className="h-10 w-10" />
              <span className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-purple-400 bg-clip-text text-transparent">
                Pinkon
              </span>
            </div>
            <nav className="hidden md:flex items-center space-x-8">
              <button 
                onClick={() => setActiveTab('home')}
                className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
              >
                Home
              </button>
              <button 
                onClick={() => setActiveTab('upload')}
                className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
              >
                Upload
              </button>
              <button 
                onClick={() => setActiveTab('compress')}
                className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
              >
                Compress
              </button>
              <button 
                onClick={() => setActiveTab('convert')}
                className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
              >
                Convert
              </button>
              <button 
                onClick={() => setActiveTab('analyze')}
                className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
              >
                Analyze
              </button>
              <button 
                onClick={() => setActiveTab('pricing')}
                className="text-sm font-medium text-primary"
              >
                Pricing
              </button>
            </nav>
            <div className="flex items-center space-x-4">
              {user ? (
                <div className="flex items-center space-x-4">
                  <div className="text-sm">
                    <div className="font-medium">{user.name}</div>
                    <div className="text-gray-500 capitalize">{user.plan} Plan</div>
                  </div>
                  <Button variant="ghost" size="sm" onClick={handleLogout}>
                    <LogOut className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <Button 
                  className="bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600"
                  onClick={() => setShowAuth(true)}
                >
                  <User className="mr-2 h-4 w-4" />
                  Login
                </Button>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">Choose Your Plan</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Select the perfect plan for your file management needs
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <Card key={index} className={`relative ${plan.popular ? 'border-purple-500 shadow-xl scale-105' : 'border-purple-100'} hover:shadow-lg transition-all`}>
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-purple-600 to-purple-500 text-white px-4 py-2 rounded-full text-sm font-medium flex items-center">
                    <Crown className="mr-1 h-4 w-4" />
                    Most Popular
                  </div>
                </div>
              )}
              <CardHeader className="text-center pb-8">
                <CardTitle className="text-2xl mb-2">{plan.name}</CardTitle>
                <div className="mb-4">
                  <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                  <span className="text-gray-500">{plan.period}</span>
                </div>
                <CardDescription className="text-base">{plan.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center">
                      <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Button 
                  className={`w-full ${plan.popular ? 'bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600' : 'bg-gray-900 hover:bg-gray-800'}`}
                  onClick={() => {
                    if (!user) {
                      setShowAuth(true)
                    } else {
                      // Mock upgrade
                      const updatedUser = {
                        ...user,
                        plan: plan.name.toLowerCase(),
                        limits: plan.limits
                      }
                      setUser(updatedUser)
                      localStorage.setItem('pinkon_user', JSON.stringify(updatedUser))
                      alert(`Upgraded to ${plan.name} plan!`)
                    }
                  }}
                >
                  {user && user.plan === plan.name.toLowerCase() ? 'Current Plan' : `Choose ${plan.name}`}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {user && (
          <Card className="mt-16 max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle>Your Usage</CardTitle>
              <CardDescription>Track your monthly usage across all features</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{user.usage.upload}</div>
                  <div className="text-sm text-gray-500">/ {user.limits.upload} Uploads</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{user.usage.compress}</div>
                  <div className="text-sm text-gray-500">/ {user.limits.compress} Compressions</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{user.usage.convert}</div>
                  <div className="text-sm text-gray-500">/ {user.limits.convert} Conversions</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{user.usage.analyze}</div>
                  <div className="text-sm text-gray-500">/ {user.limits.analyze} Analyses</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )

  const UploadPage = () => (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-white">
      <header className="bg-white/90 backdrop-blur-md border-b border-purple-100 sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <img src={pinkonLogo} alt="Pinkon" className="h-10 w-10" />
              <span className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-purple-400 bg-clip-text text-transparent">
                Pinkon
              </span>
            </div>
            <nav className="hidden md:flex items-center space-x-8">
              <button 
                onClick={() => setActiveTab('home')}
                className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
              >
                Home
              </button>
              <button 
                onClick={() => setActiveTab('upload')}
                className="text-sm font-medium text-primary"
              >
                Upload
              </button>
              <button 
                onClick={() => setActiveTab('compress')}
                className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
              >
                Compress
              </button>
              <button 
                onClick={() => setActiveTab('convert')}
                className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
              >
                Convert
              </button>
              <button 
                onClick={() => setActiveTab('analyze')}
                className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
              >
                Analyze
              </button>
              <button 
                onClick={() => setActiveTab('pricing')}
                className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
              >
                Pricing
              </button>
            </nav>
            <div className="flex items-center space-x-4">
              {user ? (
                <div className="flex items-center space-x-4">
                  <div className="text-sm">
                    <div className="font-medium">{user.name}</div>
                    <div className="text-gray-500 capitalize">{user.plan} Plan</div>
                  </div>
                  <Button variant="ghost" size="sm" onClick={handleLogout}>
                    <LogOut className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <Button 
                  className="bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600"
                  onClick={() => setShowAuth(true)}
                >
                  <User className="mr-2 h-4 w-4" />
                  Login
                </Button>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-8">Upload Your Files</h1>
          
          {user && (
            <div className="mb-6 text-center">
              <span className="text-sm text-gray-600">
                Uploads used: {user.usage.upload} / {user.limits.upload}
              </span>
            </div>
          )}
          
          <Card className="border-2 border-dashed border-purple-300 bg-purple-50/50 hover:bg-purple-50/70 transition-colors">
            <CardContent className="p-12">
              <div className="text-center">
                <Upload className="mx-auto h-16 w-16 text-purple-400 mb-4" />
                <h3 className="text-2xl font-semibold text-gray-900 mb-2">
                  Drag files here or click to browse
                </h3>
                <p className="text-gray-600 mb-6">
                  Support for images, videos, documents, and more
                </p>
                <input
                  type="file"
                  id="file-upload"
                  className="hidden"
                  onChange={(e) => {
                    if (e.target.files[0]) {
                      handleFileUpload(e.target.files[0])
                    }
                  }}
                />
                <Button 
                  className="bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600"
                  onClick={() => document.getElementById('file-upload').click()}
                  disabled={loading || (user && user.usage.upload >= user.limits.upload)}
                >
                  {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                  Choose Files
                </Button>
              </div>
            </CardContent>
          </Card>

          {error && (
            <Card className="mt-4 border-red-200 bg-red-50">
              <CardContent className="p-4">
                <p className="text-red-600">{error}</p>
              </CardContent>
            </Card>
          )}

          {result && (
            <Card className="mt-4 border-green-200 bg-green-50">
              <CardContent className="p-4">
                <p className="text-green-600">{result}</p>
              </CardContent>
            </Card>
          )}

          {uploadedFile && (
            <Card className="mt-4">
              <CardHeader>
                <CardTitle>Uploaded File</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p><strong>Filename:</strong> {uploadedFile.filename}</p>
                  <p><strong>Size:</strong> {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                  <p><strong>Type:</strong> {uploadedFile.type}</p>
                  <Button 
                    onClick={() => api.downloadFile(uploadedFile.file_id)}
                    className="mt-2"
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Download
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="grid md:grid-cols-3 gap-6 mt-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileImage className="mr-2 h-5 w-5 text-purple-600" />
                  Images
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">JPG, PNG, GIF, WebP</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Video className="mr-2 h-5 w-5 text-purple-600" />
                  Videos
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">MP4, AVI, MOV, WebM</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="mr-2 h-5 w-5 text-purple-600" />
                  Documents
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">PDF, DOC, TXT, CSV</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )

  const CompressPage = () => {
    const [quality, setQuality] = useState(80)

    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-white">
        <header className="bg-white/90 backdrop-blur-md border-b border-purple-100 sticky top-0 z-40">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <img src={pinkonLogo} alt="Pinkon" className="h-10 w-10" />
                <span className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-purple-400 bg-clip-text text-transparent">
                  Pinkon
                </span>
              </div>
              <nav className="hidden md:flex items-center space-x-8">
                <button 
                  onClick={() => setActiveTab('home')}
                  className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
                >
                  Home
                </button>
                <button 
                  onClick={() => setActiveTab('upload')}
                  className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
                >
                  Upload
                </button>
                <button 
                  onClick={() => setActiveTab('compress')}
                  className="text-sm font-medium text-primary"
                >
                  Compress
                </button>
                <button 
                  onClick={() => setActiveTab('convert')}
                  className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
                >
                  Convert
                </button>
                <button 
                  onClick={() => setActiveTab('analyze')}
                  className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
                >
                  Analyze
                </button>
                <button 
                  onClick={() => setActiveTab('pricing')}
                  className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
                >
                  Pricing
                </button>
              </nav>
              <div className="flex items-center space-x-4">
                {user ? (
                  <div className="flex items-center space-x-4">
                    <div className="text-sm">
                      <div className="font-medium">{user.name}</div>
                      <div className="text-gray-500 capitalize">{user.plan} Plan</div>
                    </div>
                    <Button variant="ghost" size="sm" onClick={handleLogout}>
                      <LogOut className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <Button 
                    className="bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600"
                    onClick={() => setShowAuth(true)}
                  >
                    <User className="mr-2 h-4 w-4" />
                    Login
                  </Button>
                )}
              </div>
            </div>
          </div>
        </header>

        <div className="container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold text-center mb-8">Compress Your Files</h1>
            
            {user && (
              <div className="mb-6 text-center">
                <span className="text-sm text-gray-600">
                  Compressions used: {user.usage.compress} / {user.limits.compress}
                </span>
              </div>
            )}
            
            <div className="grid md:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>Current File</CardTitle>
                  <CardDescription>
                    {uploadedFile ? `${uploadedFile.filename} (${(uploadedFile.size / 1024 / 1024).toFixed(2)} MB)` : 'No file uploaded'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {!uploadedFile ? (
                    <div className="text-center">
                      <p className="text-gray-600 mb-4">Please upload a file first</p>
                      <Button onClick={() => setActiveTab('upload')}>
                        Go to Upload
                      </Button>
                    </div>
                  ) : (
                    <div className="border-2 border-dashed border-purple-300 rounded-lg p-8 text-center">
                      <Zap className="mx-auto h-12 w-12 text-purple-400 mb-4" />
                      <p className="text-gray-600 mb-4">Ready to compress</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Compression Settings</CardTitle>
                  <CardDescription>Adjust quality and format options</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Quality: {quality}%</label>
                    <input 
                      type="range" 
                      min="1" 
                      max="100" 
                      value={quality}
                      onChange={(e) => setQuality(parseInt(e.target.value))}
                      className="w-full h-2 bg-purple-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <div className="flex justify-between text-sm text-gray-500 mt-1">
                      <span>Low</span>
                      <span>High</span>
                    </div>
                  </div>
                  <Button 
                    className="w-full bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600"
                    onClick={() => handleCompress(quality)}
                    disabled={!uploadedFile || loading || (user && user.usage.compress >= user.limits.compress)}
                  >
                    {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                    Compress File
                  </Button>
                </CardContent>
              </Card>
            </div>

            {error && (
              <Card className="mt-4 border-red-200 bg-red-50">
                <CardContent className="p-4">
                  <p className="text-red-600">{error}</p>
                </CardContent>
              </Card>
            )}

            {result && (
              <Card className="mt-4 border-green-200 bg-green-50">
                <CardContent className="p-4">
                  <p className="text-green-600">{result}</p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    )
  }

  const ConvertPage = () => {
    const [targetFormat, setTargetFormat] = useState('')

    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-white">
        <header className="bg-white/90 backdrop-blur-md border-b border-purple-100 sticky top-0 z-40">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <img src={pinkonLogo} alt="Pinkon" className="h-10 w-10" />
                <span className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-purple-400 bg-clip-text text-transparent">
                  Pinkon
                </span>
              </div>
              <nav className="hidden md:flex items-center space-x-8">
                <button 
                  onClick={() => setActiveTab('home')}
                  className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
                >
                  Home
                </button>
                <button 
                  onClick={() => setActiveTab('upload')}
                  className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
                >
                  Upload
                </button>
                <button 
                  onClick={() => setActiveTab('compress')}
                  className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
                >
                  Compress
                </button>
                <button 
                  onClick={() => setActiveTab('convert')}
                  className="text-sm font-medium text-primary"
                >
                  Convert
                </button>
                <button 
                  onClick={() => setActiveTab('analyze')}
                  className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
                >
                  Analyze
                </button>
                <button 
                  onClick={() => setActiveTab('pricing')}
                  className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
                >
                  Pricing
                </button>
              </nav>
              <div className="flex items-center space-x-4">
                {user ? (
                  <div className="flex items-center space-x-4">
                    <div className="text-sm">
                      <div className="font-medium">{user.name}</div>
                      <div className="text-gray-500 capitalize">{user.plan} Plan</div>
                    </div>
                    <Button variant="ghost" size="sm" onClick={handleLogout}>
                      <LogOut className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <Button 
                    className="bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600"
                    onClick={() => setShowAuth(true)}
                  >
                    <User className="mr-2 h-4 w-4" />
                    Login
                  </Button>
                )}
              </div>
            </div>
          </div>
        </header>

        <div className="container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold text-center mb-8">Convert File Formats</h1>
            
            {user && (
              <div className="mb-6 text-center">
                <span className="text-sm text-gray-600">
                  Conversions used: {user.usage.convert} / {user.limits.convert}
                </span>
              </div>
            )}
            
            <Card>
              <CardContent className="p-8">
                <div className="grid md:grid-cols-3 gap-6 items-center">
                  <div className="text-center">
                    <div className="border-2 border-dashed border-purple-300 rounded-lg p-6">
                      <RefreshCw className="mx-auto h-12 w-12 text-purple-400 mb-4" />
                      <p className="text-gray-600 mb-4">
                        {uploadedFile ? uploadedFile.filename : 'No file uploaded'}
                      </p>
                      {!uploadedFile && (
                        <Button variant="outline" onClick={() => setActiveTab('upload')}>
                          Upload File
                        </Button>
                      )}
                    </div>
                  </div>
                  
                  <div className="text-center">
                    <RefreshCw className="mx-auto h-8 w-8 text-purple-600 mb-4" />
                    <p className="font-medium">Convert to</p>
                    <select 
                      className="mt-2 w-full p-2 border border-purple-300 rounded-lg"
                      value={targetFormat}
                      onChange={(e) => setTargetFormat(e.target.value)}
                    >
                      <option value="">Select format</option>
                      <option value="jpg">JPG</option>
                      <option value="png">PNG</option>
                      <option value="webp">WebP</option>
                    </select>
                  </div>
                  
                  <div className="text-center">
                    <div className="border-2 border-purple-300 rounded-lg p-6 bg-purple-50">
                      <FileText className="mx-auto h-12 w-12 text-purple-600 mb-4" />
                      <p className="text-gray-600 mb-4">Download Result</p>
                      <Button 
                        className="bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600"
                        onClick={() => handleConvert(targetFormat)}
                        disabled={!uploadedFile || !targetFormat || loading || (user && user.usage.convert >= user.limits.convert)}
                      >
                        {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                        Convert
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {error && (
              <Card className="mt-4 border-red-200 bg-red-50">
                <CardContent className="p-4">
                  <p className="text-red-600">{error}</p>
                </CardContent>
              </Card>
            )}

            {result && (
              <Card className="mt-4 border-green-200 bg-green-50">
                <CardContent className="p-4">
                  <p className="text-green-600">{result}</p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    )
  }

  const AnalyzePage = () => (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-white">
      <header className="bg-white/90 backdrop-blur-md border-b border-purple-100 sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <img src={pinkonLogo} alt="Pinkon" className="h-10 w-10" />
              <span className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-purple-400 bg-clip-text text-transparent">
                Pinkon
              </span>
            </div>
            <nav className="hidden md:flex items-center space-x-8">
              <button 
                onClick={() => setActiveTab('home')}
                className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
              >
                Home
              </button>
              <button 
                onClick={() => setActiveTab('upload')}
                className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
              >
                Upload
              </button>
              <button 
                onClick={() => setActiveTab('compress')}
                className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
              >
                Compress
              </button>
              <button 
                onClick={() => setActiveTab('convert')}
                className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
              >
                Convert
              </button>
              <button 
                onClick={() => setActiveTab('analyze')}
                className="text-sm font-medium text-primary"
              >
                Analyze
              </button>
              <button 
                onClick={() => setActiveTab('pricing')}
                className="text-sm font-medium text-gray-600 hover:text-primary transition-colors"
              >
                Pricing
              </button>
            </nav>
            <div className="flex items-center space-x-4">
              {user ? (
                <div className="flex items-center space-x-4">
                  <div className="text-sm">
                    <div className="font-medium">{user.name}</div>
                    <div className="text-gray-500 capitalize">{user.plan} Plan</div>
                  </div>
                  <Button variant="ghost" size="sm" onClick={handleLogout}>
                    <LogOut className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <Button 
                  className="bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600"
                  onClick={() => setShowAuth(true)}
                >
                  <User className="mr-2 h-4 w-4" />
                  Login
                </Button>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-8">AI File Analysis</h1>
          
          {user && (
            <div className="mb-6 text-center">
              <span className="text-sm text-gray-600">
                Analyses used: {user.usage.analyze} / {user.limits.analyze}
              </span>
            </div>
          )}
          
          <div className="grid md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>Upload for Analysis</CardTitle>
                <CardDescription>
                  {uploadedFile ? `Analyzing: ${uploadedFile.filename}` : 'Get AI-powered insights about your files'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!uploadedFile ? (
                  <div className="border-2 border-dashed border-purple-300 rounded-lg p-8 text-center">
                    <Brain className="mx-auto h-12 w-12 text-purple-400 mb-4" />
                    <p className="text-gray-600 mb-4">Upload a file first</p>
                    <Button variant="outline" onClick={() => setActiveTab('upload')}>
                      Go to Upload
                    </Button>
                  </div>
                ) : (
                  <div className="border-2 border-dashed border-purple-300 rounded-lg p-8 text-center">
                    <Brain className="mx-auto h-12 w-12 text-purple-400 mb-4" />
                    <p className="text-gray-600 mb-4">Ready for AI analysis</p>
                    <Button 
                      variant="outline"
                      onClick={handleAnalyze}
                      disabled={loading || (user && user.usage.analyze >= user.limits.analyze)}
                    >
                      {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                      Analyze File
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Analysis Results</CardTitle>
                <CardDescription>AI-generated insights and metadata</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {result && typeof result === 'object' ? (
                    <div className="space-y-4">
                      <div className="p-4 bg-purple-50 rounded-lg">
                        <h4 className="font-medium text-purple-900">File Information</h4>
                        <div className="text-sm text-purple-700 mt-1 space-y-1">
                          <p><strong>Type:</strong> {result.file_type}</p>
                          <p><strong>Size:</strong> {result.file_size_mb} MB</p>
                          {result.dimensions && <p><strong>Dimensions:</strong> {result.dimensions}</p>}
                          {result.character_count && <p><strong>Characters:</strong> {result.character_count}</p>}
                          {result.word_count && <p><strong>Words:</strong> {result.word_count}</p>}
                        </div>
                      </div>
                      {result.ai_description && (
                        <div className="p-4 bg-purple-50 rounded-lg">
                          <h4 className="font-medium text-purple-900">AI Description</h4>
                          <p className="text-sm text-purple-700 mt-1">{result.ai_description}</p>
                        </div>
                      )}
                      {result.ai_summary && (
                        <div className="p-4 bg-purple-50 rounded-lg">
                          <h4 className="font-medium text-purple-900">AI Summary</h4>
                          <p className="text-sm text-purple-700 mt-1">{result.ai_summary}</p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="p-4 bg-purple-50 rounded-lg">
                        <h4 className="font-medium text-purple-900">Content Description</h4>
                        <p className="text-sm text-purple-700 mt-1">Upload a file to see AI analysis</p>
                      </div>
                      <div className="p-4 bg-purple-50 rounded-lg">
                        <h4 className="font-medium text-purple-900">Metadata</h4>
                        <p className="text-sm text-purple-700 mt-1">File properties and technical details</p>
                      </div>
                    </div>
                  )}
                  <Button 
                    className="w-full bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600"
                    onClick={handleAnalyze}
                    disabled={!uploadedFile || loading || (user && user.usage.analyze >= user.limits.analyze)}
                  >
                    {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                    Analyze File
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {error && (
            <Card className="mt-4 border-red-200 bg-red-50">
              <CardContent className="p-4">
                <p className="text-red-600">{error}</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )

  const renderPage = () => {
    switch (activeTab) {
      case 'upload':
        return <UploadPage />
      case 'compress':
        return <CompressPage />
      case 'convert':
        return <ConvertPage />
      case 'analyze':
        return <AnalyzePage />
      case 'pricing':
        return <PricingPage />
      default:
        return <HomePage />
    }
  }

  return (
    <>
      {renderPage()}
      {showAuth && <AuthModal />}
    </>
  )
}

export default App

