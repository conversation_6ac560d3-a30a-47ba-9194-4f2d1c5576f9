:root {
  --primary: #8B5CF6;
  --primary-light: #C4B5FD;
  --primary-dark: #7C3AED;
  --purple-50: #FAF5FF;
  --purple-100: #F3E8FF;
  --purple-200: #E9D5FF;
  --purple-300: #D8B4FE;
  --purple-400: #C084FC;
  --purple-500: #A855F7;
  --purple-600: #9333EA;
  --purple-700: #7C3AED;
  --purple-800: #6B21A8;
  --purple-900: #581C87;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #FAF5FF 0%, #FFFFFF 50%, #FAF5FF 100%);
  min-height: 100vh;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* Enhanced button styles */
.btn-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
}

/* Card enhancements */
.card-hover {
  transition: all 0.3s ease;
  border: 1px solid var(--purple-200);
}

.card-hover:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(139, 92, 246, 0.15);
  border-color: var(--primary-light);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced hero section */
.hero-gradient {
  background: linear-gradient(135deg, 
    var(--purple-50) 0%, 
    #FFFFFF 25%, 
    var(--purple-50) 50%, 
    #FFFFFF 75%, 
    var(--purple-50) 100%);
  position: relative;
  overflow: hidden;
}

.hero-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(196, 181, 253, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* Animated background elements */
.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.floating-shapes::before,
.floating-shapes::after {
  content: '';
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  opacity: 0.1;
  animation: float 20s infinite ease-in-out;
}

.floating-shapes::before {
  width: 300px;
  height: 300px;
  top: 10%;
  left: -10%;
  animation-delay: 0s;
}

.floating-shapes::after {
  width: 200px;
  height: 200px;
  bottom: 10%;
  right: -5%;
  animation-delay: 10s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-30px) rotate(120deg);
  }
  66% {
    transform: translateY(30px) rotate(240deg);
  }
}

/* Enhanced form styles */
.form-input {
  border: 2px solid var(--purple-200);
  border-radius: 8px;
  padding: 12px 16px;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

/* Enhanced navigation */
.nav-link {
  position: relative;
  transition: all 0.3s ease;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary);
  transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

/* File upload area */
.upload-area {
  border: 3px dashed var(--purple-300);
  border-radius: 12px;
  background: linear-gradient(135deg, var(--purple-50) 0%, rgba(255, 255, 255, 0.8) 100%);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.upload-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.upload-area:hover {
  border-color: var(--primary);
  background: linear-gradient(135deg, var(--purple-100) 0%, rgba(255, 255, 255, 0.9) 100%);
  transform: scale(1.02);
}

.upload-area:hover::before {
  left: 100%;
}

/* Progress indicators */
.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--purple-200);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Enhanced pricing cards */
.pricing-card {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.pricing-card.popular {
  transform: scale(1.05);
  border: 2px solid var(--primary);
  box-shadow: 0 20px 40px rgba(139, 92, 246, 0.2);
}

.pricing-card.popular::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
}

/* Feature icons */
.feature-icon {
  background: linear-gradient(135deg, var(--purple-100) 0%, var(--purple-50) 100%);
  border-radius: 16px;
  padding: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.feature-icon:hover {
  transform: scale(1.1) rotate(5deg);
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--purple-100) 100%);
}

/* Loading spinner */
.spinner {
  border: 3px solid var(--purple-200);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Success and error states */
.success-message {
  background: linear-gradient(135deg, #10B981 0%, #34D399 100%);
  color: white;
  padding: 16px;
  border-radius: 8px;
  margin: 16px 0;
}

.error-message {
  background: linear-gradient(135deg, #EF4444 0%, #F87171 100%);
  color: white;
  padding: 16px;
  border-radius: 8px;
  margin: 16px 0;
}

/* Modal overlay */
.modal-overlay {
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design enhancements */
@media (max-width: 768px) {
  .hero-gradient h1 {
    font-size: 2.5rem;
  }
  
  .pricing-card.popular {
    transform: none;
    margin-bottom: 2rem;
  }
  
  .floating-shapes::before,
  .floating-shapes::after {
    display: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1a1a1a;
    --text-primary: #ffffff;
    --text-secondary: #a0a0a0;
  }
}

/* Accessibility improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for better accessibility */
button:focus,
input:focus,
select:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .gradient-text {
    -webkit-text-fill-color: var(--primary);
    background: none;
  }
  
  .btn-primary {
    background: var(--primary);
    border: 2px solid var(--primary-dark);
  }
}

