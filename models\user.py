from datetime import datetime
import sqlite3
import hashlib
import secrets
import json

class User:
    def __init__(self, db_path='users.db'):
        self.db_path = db_path
        self.init_db()
    
    def init_db(self):
        """Initialize the database with user tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                password_hash TEXT NOT NULL,
                plan TEXT DEFAULT 'free',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                oauth_provider TEXT,
                oauth_id TEXT,
                is_active BOOLEAN DEFAULT TRUE
            )
        ''')
        
        # Usage tracking table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_usage (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                feature TEXT NOT NULL,
                usage_count INTEGER DEFAULT 0,
                month_year TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                UNIQUE(user_id, feature, month_year)
            )
        ''')
        
        # Sessions table for authentication
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                session_token TEXT UNIQUE NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def hash_password(self, password):
        """Hash a password for storing"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def generate_session_token(self):
        """Generate a secure session token"""
        return secrets.token_urlsafe(32)
    
    def create_user(self, email, name, password, oauth_provider=None, oauth_id=None):
        """Create a new user"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            password_hash = self.hash_password(password) if password else None
            cursor.execute('''
                INSERT INTO users (email, name, password_hash, oauth_provider, oauth_id)
                VALUES (?, ?, ?, ?, ?)
            ''', (email, name, password_hash, oauth_provider, oauth_id))
            
            user_id = cursor.lastrowid
            
            # Initialize usage tracking for current month
            current_month = datetime.now().strftime('%Y-%m')
            features = ['upload', 'compress', 'convert', 'analyze']
            
            for feature in features:
                cursor.execute('''
                    INSERT INTO user_usage (user_id, feature, usage_count, month_year)
                    VALUES (?, ?, 0, ?)
                ''', (user_id, feature, current_month))
            
            conn.commit()
            return user_id
            
        except sqlite3.IntegrityError:
            return None  # User already exists
        finally:
            conn.close()
    
    def authenticate_user(self, email, password):
        """Authenticate a user with email and password"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        password_hash = self.hash_password(password)
        cursor.execute('''
            SELECT id, email, name, plan, created_at
            FROM users 
            WHERE email = ? AND password_hash = ? AND is_active = TRUE
        ''', (email, password_hash))
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            return {
                'id': user[0],
                'email': user[1],
                'name': user[2],
                'plan': user[3],
                'created_at': user[4]
            }
        return None
    
    def create_session(self, user_id):
        """Create a new session for a user"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        session_token = self.generate_session_token()
        expires_at = datetime.now().timestamp() + (30 * 24 * 60 * 60)  # 30 days
        
        cursor.execute('''
            INSERT INTO user_sessions (user_id, session_token, expires_at)
            VALUES (?, ?, ?)
        ''', (user_id, session_token, expires_at))
        
        conn.commit()
        conn.close()
        
        return session_token
    
    def get_user_by_session(self, session_token):
        """Get user by session token"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT u.id, u.email, u.name, u.plan, u.created_at
            FROM users u
            JOIN user_sessions s ON u.id = s.user_id
            WHERE s.session_token = ? AND s.expires_at > ? AND u.is_active = TRUE
        ''', (session_token, datetime.now().timestamp()))
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            return {
                'id': user[0],
                'email': user[1],
                'name': user[2],
                'plan': user[3],
                'created_at': user[4]
            }
        return None
    
    def get_user_usage(self, user_id):
        """Get user's current month usage"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        current_month = datetime.now().strftime('%Y-%m')
        cursor.execute('''
            SELECT feature, usage_count
            FROM user_usage
            WHERE user_id = ? AND month_year = ?
        ''', (user_id, current_month))
        
        usage_data = cursor.fetchall()
        conn.close()
        
        usage = {}
        for feature, count in usage_data:
            usage[feature] = count
            
        # Ensure all features are present
        for feature in ['upload', 'compress', 'convert', 'analyze']:
            if feature not in usage:
                usage[feature] = 0
                
        return usage
    
    def increment_usage(self, user_id, feature):
        """Increment usage count for a feature"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        current_month = datetime.now().strftime('%Y-%m')
        
        # Try to increment existing record
        cursor.execute('''
            UPDATE user_usage 
            SET usage_count = usage_count + 1
            WHERE user_id = ? AND feature = ? AND month_year = ?
        ''', (user_id, feature, current_month))
        
        # If no record was updated, create a new one
        if cursor.rowcount == 0:
            cursor.execute('''
                INSERT INTO user_usage (user_id, feature, usage_count, month_year)
                VALUES (?, ?, 1, ?)
            ''', (user_id, feature, current_month))
        
        conn.commit()
        conn.close()
    
    def get_plan_limits(self, plan):
        """Get limits for a specific plan"""
        limits = {
            'free': {
                'upload': 5,
                'compress': 5,
                'convert': 5,
                'analyze': 5
            },
            'pro': {
                'upload': 100,
                'compress': 100,
                'convert': 100,
                'analyze': 100
            },
            'premium': {
                'upload': 200,
                'compress': 200,
                'convert': 200,
                'analyze': 200
            }
        }
        return limits.get(plan, limits['free'])
    
    def update_user_plan(self, user_id, new_plan):
        """Update user's subscription plan"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE users 
            SET plan = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (new_plan, user_id))
        
        conn.commit()
        conn.close()
        
        return cursor.rowcount > 0
    
    def delete_session(self, session_token):
        """Delete a session (logout)"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM user_sessions WHERE session_token = ?', (session_token,))
        
        conn.commit()
        conn.close()
        
        return cursor.rowcount > 0

