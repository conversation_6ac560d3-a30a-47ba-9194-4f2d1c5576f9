import requests
import base64
import json
from PIL import Image
import os

class AIService:
    def __init__(self):
        # DeepSeek API configuration
        self.api_base_url = "https://api.deepseek.com"
        self.api_key = os.getenv('DEEPSEEK_API_KEY', 'demo_key')  # Set your DeepSeek API key
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
    
    def analyze_image(self, image_path):
        """Analyze an image using DeepSeek Vision model"""
        try:
            # Read and encode image
            with open(image_path, 'rb') as image_file:
                image_data = base64.b64encode(image_file.read()).decode('utf-8')
            
            # Get image format
            image_format = image_path.split('.')[-1].lower()
            if image_format == 'jpg':
                image_format = 'jpeg'
            
            # Prepare the request
            payload = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "Analyze this image in detail. Describe what you see, including objects, people, colors, composition, style, and any notable features. Provide insights about the image's content, quality, and potential use cases."
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/{image_format};base64,{image_data}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 500,
                "temperature": 0.7
            }
            
            # Make API request
            response = requests.post(
                f"{self.api_base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'description': result['choices'][0]['message']['content'],
                    'model': 'deepseek-chat',
                    'tokens_used': result.get('usage', {}).get('total_tokens', 0)
                }
            else:
                return {
                    'success': False,
                    'error': f"API request failed: {response.status_code} - {response.text}"
                }
                
        except Exception as e:
            # Fallback to basic image analysis
            return self._fallback_image_analysis(image_path, str(e))
    
    def analyze_text(self, text_content, file_type='txt'):
        """Analyze text content using DeepSeek"""
        try:
            # Truncate text if too long
            max_chars = 4000
            if len(text_content) > max_chars:
                text_content = text_content[:max_chars] + "..."
            
            # Prepare analysis prompt based on file type
            if file_type == 'csv':
                prompt = f"Analyze this CSV data and provide insights about its structure, content, patterns, and potential use cases:\n\n{text_content}"
            else:
                prompt = f"Analyze this text content and provide a comprehensive summary, key themes, sentiment, and insights:\n\n{text_content}"
            
            payload = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": 400,
                "temperature": 0.7
            }
            
            # Make API request
            response = requests.post(
                f"{self.api_base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'summary': result['choices'][0]['message']['content'],
                    'model': 'deepseek-chat',
                    'tokens_used': result.get('usage', {}).get('total_tokens', 0)
                }
            else:
                return {
                    'success': False,
                    'error': f"API request failed: {response.status_code} - {response.text}"
                }
                
        except Exception as e:
            # Fallback to basic text analysis
            return self._fallback_text_analysis(text_content, str(e))
    
    def analyze_document(self, file_path, file_type):
        """Analyze document files"""
        try:
            if file_type in ['txt', 'csv']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return self.analyze_text(content, file_type)
            elif file_type == 'pdf':
                # For PDF files, we would need additional libraries like PyPDF2
                return {
                    'success': False,
                    'error': 'PDF analysis not implemented yet. Please use text or image files.'
                }
            else:
                return {
                    'success': False,
                    'error': f'Unsupported document type: {file_type}'
                }
        except Exception as e:
            return {
                'success': False,
                'error': f'Document analysis failed: {str(e)}'
            }
    
    def _fallback_image_analysis(self, image_path, error_msg):
        """Fallback image analysis when API fails"""
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                mode = img.mode
                format_name = img.format
                
                # Basic analysis
                aspect_ratio = round(width / height, 2)
                megapixels = round((width * height) / 1000000, 1)
                
                # Determine image characteristics
                if aspect_ratio > 1.5:
                    orientation = "landscape"
                elif aspect_ratio < 0.7:
                    orientation = "portrait"
                else:
                    orientation = "square"
                
                size_category = "large" if megapixels > 5 else "medium" if megapixels > 1 else "small"
                
                description = f"This is a {size_category} {orientation} {format_name} image with dimensions {width}x{height} pixels ({megapixels}MP). "
                description += f"The image uses {mode} color mode. "
                description += "DeepSeek API analysis is currently unavailable, but the image appears to be properly formatted and readable."
                
                return {
                    'success': True,
                    'description': description,
                    'model': 'fallback-basic',
                    'error': f'API unavailable: {error_msg}'
                }
        except Exception as fallback_error:
            return {
                'success': False,
                'error': f'Both API and fallback analysis failed: {str(fallback_error)}'
            }
    
    def _fallback_text_analysis(self, text_content, error_msg):
        """Fallback text analysis when API fails"""
        try:
            # Basic text statistics
            char_count = len(text_content)
            word_count = len(text_content.split())
            line_count = len(text_content.splitlines())
            
            # Simple analysis
            avg_word_length = round(char_count / word_count, 1) if word_count > 0 else 0
            
            # Basic content detection
            has_numbers = any(char.isdigit() for char in text_content)
            has_punctuation = any(char in '.,!?;:' for char in text_content)
            
            summary = f"This text contains {word_count} words and {char_count} characters across {line_count} lines. "
            summary += f"Average word length is {avg_word_length} characters. "
            
            if has_numbers and ',' in text_content:
                summary += "The text appears to contain structured data or numerical information. "
            elif has_punctuation and word_count > 50:
                summary += "This appears to be a well-formatted document or article. "
            else:
                summary += "This appears to be plain text content. "
            
            summary += "DeepSeek API analysis is currently unavailable, but basic text statistics have been provided."
            
            return {
                'success': True,
                'summary': summary,
                'model': 'fallback-basic',
                'error': f'API unavailable: {error_msg}'
            }
        except Exception as fallback_error:
            return {
                'success': False,
                'error': f'Both API and fallback analysis failed: {str(fallback_error)}'
            }
    
    def get_analysis_for_file(self, file_path, file_type):
        """Main method to get AI analysis for any supported file type"""
        file_size = os.path.getsize(file_path)
        file_size_mb = round(file_size / (1024 * 1024), 2)
        
        # Base analysis result
        result = {
            'file_type': file_type,
            'file_size_mb': file_size_mb,
            'analysis_timestamp': None
        }
        
        # Perform specific analysis based on file type
        if file_type.lower() in ['jpg', 'jpeg', 'png', 'webp', 'gif']:
            ai_result = self.analyze_image(file_path)
            if ai_result['success']:
                result['ai_description'] = ai_result['description']
                result['model_used'] = ai_result['model']
                if 'tokens_used' in ai_result:
                    result['tokens_used'] = ai_result['tokens_used']
            else:
                result['ai_description'] = f"Image analysis failed: {ai_result['error']}"
            
            # Add image-specific metadata
            try:
                with Image.open(file_path) as img:
                    result['dimensions'] = f"{img.width}x{img.height}"
                    result['color_mode'] = img.mode
                    result['format'] = img.format
            except:
                pass
                
        elif file_type.lower() in ['txt', 'csv']:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    result['character_count'] = len(content)
                    result['word_count'] = len(content.split())
                    result['line_count'] = len(content.splitlines())
                    
                    ai_result = self.analyze_text(content, file_type)
                    if ai_result['success']:
                        result['ai_summary'] = ai_result['summary']
                        result['model_used'] = ai_result['model']
                        if 'tokens_used' in ai_result:
                            result['tokens_used'] = ai_result['tokens_used']
                    else:
                        result['ai_summary'] = f"Text analysis failed: {ai_result['error']}"
            except Exception as e:
                result['ai_summary'] = f"Failed to read text file: {str(e)}"
        
        else:
            result['ai_description'] = f"File type '{file_type}' is not supported for AI analysis yet."
        
        return result

# Global AI service instance
ai_service = AIService()

