// API configuration
const API_BASE_URL = '/api';

// Get auth token from localStorage
const getAuthToken = () => {
  const user = localStorage.getItem('pinkon_user');
  if (user) {
    const userData = JSON.parse(user);
    return userData.session_token;
  }
  return null;
};

// Create headers with authentication
const createHeaders = (includeAuth = true) => {
  const headers = {
    'Content-Type': 'application/json',
  };
  
  if (includeAuth) {
    const token = getAuthToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
  }
  
  return headers;
};

// API functions
export const api = {
  // Authentication APIs
  auth: {
    register: async (email, password, name) => {
      try {
        const response = await fetch(`${API_BASE_URL}/auth/register`, {
          method: 'POST',
          headers: createHeaders(false),
          body: JSON.stringify({ email, password, name })
        });
        return await response.json();
      } catch (error) {
        return { success: false, error: error.message };
      }
    },

    login: async (email, password) => {
      try {
        const response = await fetch(`${API_BASE_URL}/auth/login`, {
          method: 'POST',
          headers: createHeaders(false),
          body: JSON.stringify({ email, password })
        });
        return await response.json();
      } catch (error) {
        return { success: false, error: error.message };
      }
    },

    oauthLogin: async (provider, userData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/auth/oauth/${provider}`, {
          method: 'POST',
          headers: createHeaders(false),
          body: JSON.stringify(userData)
        });
        return await response.json();
      } catch (error) {
        return { success: false, error: error.message };
      }
    },

    logout: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/auth/logout`, {
          method: 'POST',
          headers: createHeaders(true)
        });
        return await response.json();
      } catch (error) {
        return { success: false, error: error.message };
      }
    },

    getCurrentUser: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/auth/me`, {
          method: 'GET',
          headers: createHeaders(true)
        });
        return await response.json();
      } catch (error) {
        return { success: false, error: error.message };
      }
    },

    upgradePlan: async (plan) => {
      try {
        const response = await fetch(`${API_BASE_URL}/auth/upgrade`, {
          method: 'POST',
          headers: createHeaders(true),
          body: JSON.stringify({ plan })
        });
        return await response.json();
      } catch (error) {
        return { success: false, error: error.message };
      }
    }
  },

  // File operations
  uploadFile: async (file) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      const token = getAuthToken();
      const headers = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${API_BASE_URL}/upload`, {
        method: 'POST',
        headers: headers,
        body: formData
      });

      return await response.json();
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  compressFile: async (fileId, quality) => {
    try {
      const response = await fetch(`${API_BASE_URL}/compress`, {
        method: 'POST',
        headers: createHeaders(true),
        body: JSON.stringify({
          file_id: fileId,
          quality: quality
        })
      });

      return await response.json();
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  convertFile: async (fileId, targetFormat) => {
    try {
      const response = await fetch(`${API_BASE_URL}/convert`, {
        method: 'POST',
        headers: createHeaders(true),
        body: JSON.stringify({
          file_id: fileId,
          target_format: targetFormat
        })
      });

      return await response.json();
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  analyzeFile: async (fileId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/analyze`, {
        method: 'POST',
        headers: createHeaders(true),
        body: JSON.stringify({
          file_id: fileId
        })
      });

      return await response.json();
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  downloadFile: async (fileId) => {
    try {
      const token = getAuthToken();
      const headers = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${API_BASE_URL}/download/${fileId}`, {
        method: 'GET',
        headers: headers
      });

      if (response.ok) {
        // Create download link
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `file_${fileId}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        return { success: true, message: 'Download started' };
      } else {
        const errorData = await response.json();
        return { success: false, error: errorData.error || 'Download failed' };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  listFiles: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/list`, {
        method: 'GET',
        headers: createHeaders(true)
      });

      return await response.json();
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
};

// Helper functions for user management
export const userHelpers = {
  saveUser: (userData) => {
    localStorage.setItem('pinkon_user', JSON.stringify(userData));
  },

  getUser: () => {
    const userData = localStorage.getItem('pinkon_user');
    return userData ? JSON.parse(userData) : null;
  },

  removeUser: () => {
    localStorage.removeItem('pinkon_user');
  },

  updateUser: (userData) => {
    const currentUser = userHelpers.getUser();
    if (currentUser) {
      const updatedUser = { ...currentUser, ...userData };
      userHelpers.saveUser(updatedUser);
      return updatedUser;
    }
    return null;
  }
};

