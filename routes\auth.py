from flask import Blueprint, request, jsonify, session
from models.user import User
import re

auth_bp = Blueprint('auth', __name__)
user_model = User()

def validate_email(email):
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """Validate password strength"""
    if len(password) < 6:
        return False, "Password must be at least 6 characters long"
    return True, "Password is valid"

@auth_bp.route('/register', methods=['POST'])
def register():
    """Register a new user"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['email', 'name', 'password']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'error': f'{field} is required'
                }), 400
        
        email = data['email'].lower().strip()
        name = data['name'].strip()
        password = data['password']
        
        # Validate email format
        if not validate_email(email):
            return jsonify({
                'success': False,
                'error': 'Invalid email format'
            }), 400
        
        # Validate password
        is_valid, message = validate_password(password)
        if not is_valid:
            return jsonify({
                'success': False,
                'error': message
            }), 400
        
        # Create user
        user_id = user_model.create_user(email, name, password)
        
        if user_id is None:
            return jsonify({
                'success': False,
                'error': 'User with this email already exists'
            }), 409
        
        # Create session
        session_token = user_model.create_session(user_id)
        
        # Get user data
        user_data = user_model.get_user_by_session(session_token)
        usage = user_model.get_user_usage(user_id)
        limits = user_model.get_plan_limits(user_data['plan'])
        
        return jsonify({
            'success': True,
            'user': {
                'id': user_data['id'],
                'email': user_data['email'],
                'name': user_data['name'],
                'plan': user_data['plan'],
                'usage': usage,
                'limits': limits
            },
            'session_token': session_token
        }), 201
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Registration failed: ' + str(e)
        }), 500

@auth_bp.route('/login', methods=['POST'])
def login():
    """Login user"""
    try:
        data = request.get_json()
        
        # Validate required fields
        if not data.get('email') or not data.get('password'):
            return jsonify({
                'success': False,
                'error': 'Email and password are required'
            }), 400
        
        email = data['email'].lower().strip()
        password = data['password']
        
        # Authenticate user
        user_data = user_model.authenticate_user(email, password)
        
        if not user_data:
            return jsonify({
                'success': False,
                'error': 'Invalid email or password'
            }), 401
        
        # Create session
        session_token = user_model.create_session(user_data['id'])
        
        # Get usage data
        usage = user_model.get_user_usage(user_data['id'])
        limits = user_model.get_plan_limits(user_data['plan'])
        
        return jsonify({
            'success': True,
            'user': {
                'id': user_data['id'],
                'email': user_data['email'],
                'name': user_data['name'],
                'plan': user_data['plan'],
                'usage': usage,
                'limits': limits
            },
            'session_token': session_token
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Login failed: ' + str(e)
        }), 500

@auth_bp.route('/oauth/<provider>', methods=['POST'])
def oauth_login(provider):
    """OAuth login (mock implementation)"""
    try:
        data = request.get_json()
        
        if provider not in ['google', 'github']:
            return jsonify({
                'success': False,
                'error': 'Unsupported OAuth provider'
            }), 400
        
        # Mock OAuth data - in real implementation, verify with OAuth provider
        email = data.get('email', f'user@{provider}.com')
        name = data.get('name', f'{provider.title()} User')
        oauth_id = data.get('id', 'mock_oauth_id')
        
        # Try to find existing user
        user_data = user_model.authenticate_user(email, 'oauth_placeholder')
        
        if not user_data:
            # Create new user
            user_id = user_model.create_user(
                email=email,
                name=name,
                password='oauth_placeholder',
                oauth_provider=provider,
                oauth_id=oauth_id
            )
            
            if user_id is None:
                return jsonify({
                    'success': False,
                    'error': 'Failed to create user account'
                }), 500
        else:
            user_id = user_data['id']
        
        # Create session
        session_token = user_model.create_session(user_id)
        
        # Get user data
        user_data = user_model.get_user_by_session(session_token)
        usage = user_model.get_user_usage(user_id)
        limits = user_model.get_plan_limits(user_data['plan'])
        
        return jsonify({
            'success': True,
            'user': {
                'id': user_data['id'],
                'email': user_data['email'],
                'name': user_data['name'],
                'plan': user_data['plan'],
                'usage': usage,
                'limits': limits
            },
            'session_token': session_token
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'OAuth login failed: {str(e)}'
        }), 500

@auth_bp.route('/logout', methods=['POST'])
def logout():
    """Logout user"""
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'success': False,
                'error': 'No session token provided'
            }), 401
        
        session_token = auth_header.split(' ')[1]
        
        # Delete session
        success = user_model.delete_session(session_token)
        
        return jsonify({
            'success': success,
            'message': 'Logged out successfully' if success else 'Session not found'
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Logout failed: ' + str(e)
        }), 500

@auth_bp.route('/me', methods=['GET'])
def get_current_user():
    """Get current user data"""
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'success': False,
                'error': 'No session token provided'
            }), 401
        
        session_token = auth_header.split(' ')[1]
        
        # Get user by session
        user_data = user_model.get_user_by_session(session_token)
        
        if not user_data:
            return jsonify({
                'success': False,
                'error': 'Invalid or expired session'
            }), 401
        
        # Get usage data
        usage = user_model.get_user_usage(user_data['id'])
        limits = user_model.get_plan_limits(user_data['plan'])
        
        return jsonify({
            'success': True,
            'user': {
                'id': user_data['id'],
                'email': user_data['email'],
                'name': user_data['name'],
                'plan': user_data['plan'],
                'usage': usage,
                'limits': limits
            }
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Failed to get user data: ' + str(e)
        }), 500

@auth_bp.route('/upgrade', methods=['POST'])
def upgrade_plan():
    """Upgrade user plan"""
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'success': False,
                'error': 'No session token provided'
            }), 401
        
        session_token = auth_header.split(' ')[1]
        data = request.get_json()
        
        # Get user by session
        user_data = user_model.get_user_by_session(session_token)
        
        if not user_data:
            return jsonify({
                'success': False,
                'error': 'Invalid or expired session'
            }), 401
        
        new_plan = data.get('plan')
        if new_plan not in ['free', 'pro', 'premium']:
            return jsonify({
                'success': False,
                'error': 'Invalid plan type'
            }), 400
        
        # Update user plan
        success = user_model.update_user_plan(user_data['id'], new_plan)
        
        if success:
            # Get updated user data
            updated_user = user_model.get_user_by_session(session_token)
            usage = user_model.get_user_usage(user_data['id'])
            limits = user_model.get_plan_limits(new_plan)
            
            return jsonify({
                'success': True,
                'user': {
                    'id': updated_user['id'],
                    'email': updated_user['email'],
                    'name': updated_user['name'],
                    'plan': updated_user['plan'],
                    'usage': usage,
                    'limits': limits
                },
                'message': f'Successfully upgraded to {new_plan} plan'
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to update plan'
            }), 500
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Plan upgrade failed: ' + str(e)
        }), 500

